import type { ColumnDef } from '@tanstack/vue-table'

// 分页类型
export interface Pagination {
  current_page: number
  per_page: number
  total_pages: number
  total: number
}

// API 响应类型
export interface ListResponse<T> {
  data: T[]
  pagination: Pagination
}

// 直接使用 TanStack Table 的 ColumnDef
export type Column<T = any> = ColumnDef<T>

// CURD API 接口定义
export interface CurdApi<T = any> {
  getList: (params?: ListParams) => Promise<ListResponse<T>>
  getItem: (id: string | number) => Promise<T>
  createItem: (data: Partial<T>) => Promise<T>
  updateItem: (id: string | number, data: Partial<T>) => Promise<T>
  deleteItem: (id: string | number, query?: Record<string, any>) => Promise<T>
  restoreItem: (id: string | number) => Promise<T>
}

// 列表查询参数
export interface ListParams {
  page?: number
  per_page?: number
  sort_field?: string
  sort_order?: 'asc' | 'desc'
  [key: string]: any
}

// CURD 配置项
export interface CurdConfig<T = any> {
  api: CurdApi<T>
  columns: Column<T>[]
  primaryKey?: string
  searchable?: boolean
  creatable?: boolean
  editable?: boolean
  deletable?: boolean
  batchDeletable?: boolean
  recyclable?: boolean
  pageSize?: number
  pageSizeOptions?: number[]
  btnText?: ButtonText
}

export interface ButtonText {
  create?: string
  edit?: string
  delete?: string
  batchDelete?: string
  search?: string
  refresh?: string
  export?: string
  import?: string
}

// CURD 操作钩子
export interface CurdHooks<T = any> {
  // 列表相关
  beforeList?: (params: ListParams) => ListParams | Promise<ListParams> | false | Promise<false>
  afterList?: (response: ListResponse<T>) => void | Promise<void> | false | Promise<false>

  // 创建相关
  beforeCreate?: (data: Partial<T>) => Partial<T> | Promise<Partial<T>> | false | Promise<false>
  afterCreate?: (data: T) => void | Promise<void> | false | Promise<false>

  // 更新相关
  beforeUpdate?: (id: string | number, data: Partial<T>) => Partial<T> | Promise<Partial<T>> | false | Promise<false>
  afterUpdate?: (data: T) => void | Promise<void> | false | Promise<false>

  // 删除相关
  beforeDelete?: (id: string | number) => void | Promise<void> | false | Promise<false>
  afterDelete?: (id: string | number) => void | Promise<void> | false | Promise<false>

  // 批量删除相关
  beforeBatchDelete?: (ids: (string | number)[]) => void | Promise<void> | false | Promise<false>
  afterBatchDelete?: (ids: (string | number)[]) => void | Promise<void> | false | Promise<false>

  // 通用保存相关（create 和 update 的通用版本）
  beforeSave?: (data: Partial<T>, mode: 'create' | 'update', id?: string | number) => Partial<T> | Promise<Partial<T>> | false | Promise<false>
  afterSave?: (data: T, mode: 'create' | 'update') => void | Promise<void> | false | Promise<false>

  // 表单相关
  beforeFormSubmit?: (formData: Record<string, any>, mode: 'create' | 'update') => Record<string, any> | Promise<Record<string, any>> | false | Promise<false>
  afterFormSubmit?: (result: T, mode: 'create' | 'update') => void | Promise<void> | false | Promise<false>

  // 表单验证相关
  beforeValidate?: (formData: Record<string, any>) => Record<string, any> | Promise<Record<string, any>> | false | Promise<false>
  afterValidate?: (formData: Record<string, any>, isValid: boolean, errors?: any[]) => void | Promise<void> | false | Promise<false>

  // 选择相关
  beforeSelect?: (selectedIds: (string | number)[]) => void | Promise<void> | false | Promise<false>
  afterSelect?: (selectedIds: (string | number)[], selectedRows: T[]) => void | Promise<void> | false | Promise<false>

  // 搜索相关
  beforeSearch?: (searchParams: Record<string, any>) => Record<string, any> | Promise<Record<string, any>> | false | Promise<false>
  afterSearch?: (searchParams: Record<string, any>, results: T[]) => void | Promise<void> | false | Promise<false>

  // 排序相关
  beforeSort?: (field: string, order: 'asc' | 'desc') => { field: string, order: 'asc' | 'desc' } | Promise<{ field: string, order: 'asc' | 'desc' }> | false | Promise<false>
  afterSort?: (field: string, order: 'asc' | 'desc', results: T[]) => void | Promise<void> | false | Promise<false>

  // 刷新相关
  beforeRefresh?: () => void | Promise<void> | false | Promise<false>
  afterRefresh?: (results: T[]) => void | Promise<void> | false | Promise<false>

  // 错误处理
  onError?: (error: Error, operation: string, context?: any) => void | Promise<void> | false | Promise<false>
}

// 表单字段类型 - 基于 shadcn-vue 组件库和实际开发需求
export type FormFieldType
  = | 'text' // 基础文本输入
    | 'email' // 邮箱输入
    | 'password' // 密码输入
    | 'url' // URL 输入
    | 'tel' // 电话号码输入
    | 'search' // 搜索输入
    | 'number' // 数字输入
    | 'number-field' // shadcn NumberField 组件
    | 'textarea' // 多行文本
    | 'rich-text' // 富文本编辑器
    | 'select' // 下拉选择
    | 'combobox' // 可搜索下拉选择
    | 'multiselect' // 多选下拉
    | 'checkbox' // 复选框
    | 'checkbox-group' // 复选框组
    | 'radio' // 单选框
    | 'radio-group' // 单选框组
    | 'switch' // 开关
    | 'slider' // 滑块
    | 'range-slider' // 范围滑块
    | 'date' // 日期输入
    | 'datetime' // 日期时间输入
    | 'time' // 时间输入
    | 'calendar' // 日历选择器
    | 'date-range' // 日期范围选择
    | 'date-picker' // 日期选择器（弹出式）
    | 'tags-input' // 标签输入
    | 'pin-input' // PIN 码输入
    | 'file' // 文件上传
    | 'file-multiple' // 多文件上传
    | 'image' // 图片上传
    | 'image-multiple' // 多图片上传
    | 'avatar' // 头像上传
    | 'color' // 颜色选择器
    | 'rating' // 评分组件
    | 'toggle' // 切换按钮
    | 'badge' // 徽章输入
    | 'progress' // 进度条
    | 'json' // JSON 编辑器
    | 'code' // 代码编辑器
    | 'markdown' // Markdown 编辑器
    | 'custom' // 自定义组件

// 选择项配置
export interface SelectOption {
  label: string
  value: any
  disabled?: boolean
}

// 验证规则
export interface ValidationRule {
  required?: boolean
  message?: string
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (value: any, formData: any) => boolean | string
}

// 添加远程搜索配置接口
export interface RemoteSearchConfig {
  /** 搜索API函数 */
  searchApi: (query: string) => Promise<{ label: string, value: any, [key: string]: any }[]>
  /** 搜索防抖延迟(ms) */
  debounceMs?: number
  /** 最小搜索字符数 */
  minSearchLength?: number
  /** 空搜索时是否显示默认选项 */
  showDefaultOptions?: boolean
  /** 默认选项 */
  defaultOptions?: { label: string, value: any, [key: string]: any }[]
  /** 自定义选项渲染函数 */
  renderOption?: (option: any) => string
}

// 字段联动配置接口
export interface FieldLinkageConfig {
  /** 依赖的字段key数组 */
  dependsOn: string[]
  /** 联动处理函数，接收依赖字段的值和完整表单数据，返回新的选项或值 */
  handler: (dependentValues: Record<string, any>, formData: Record<string, any>) =>
    Promise<{ label: string, value: any, [key: string]: any }[]> | { label: string, value: any, [key: string]: any }[]
  /** 依赖字段变化时是否清空当前字段值 */
  clearOnDependentChange?: boolean
  /** 联动延迟时间(ms)，用于防抖 */
  debounceMs?: number
  /** 加载选项时的占位符 */
  loadingPlaceholder?: string
  /** 失败时的提示信息 */
  errorMessage?: string
}

// 字段值变化监听器配置
export interface FieldWatchConfig {
  /** 要监听的字段key数组 */
  watchFields: string[]
  /** 值变化处理函数 */
  handler: (changedField: string, newValue: any, oldValue: any, formData: Record<string, any>) => void
}

// 字段级联配置
export interface FieldCascadeConfig {
  /** 级联字段映射，key为当前字段值，value为目标字段的新值 */
  cascadeMap: Record<string, any>
  /** 目标字段key */
  targetField: string
  /** 是否强制更新目标字段（即使目标字段已有值） */
  forceUpdate?: boolean
}

// 文件上传配置
export interface FileUploadConfig {
  /** 接受的文件类型，如 '.jpg,.png,.pdf' 或 'image/*' */
  accept?: string
  /** 文件大小限制（字节） */
  maxSize?: number
  /** 是否支持多文件上传 */
  multiple?: boolean
  /** 上传API端点 */
  uploadUrl?: string
  /** 自定义上传函数 */
  customUpload?: (file: File) => Promise<{ url: string, [key: string]: any }>
  /** 文件预览配置 */
  preview?: boolean
  /** 拖拽上传 */
  dragDrop?: boolean
}

// 日期范围配置
export interface DateRangeConfig {
  /** 开始日期字段名 */
  startKey?: string
  /** 结束日期字段名 */
  endKey?: string
  /** 最小日期 */
  minDate?: Date | string
  /** 最大日期 */
  maxDate?: Date | string
  /** 日期格式 */
  format?: string
}

// 滑块配置
export interface SliderConfig {
  /** 最小值 */
  min?: number
  /** 最大值 */
  max?: number
  /** 步长 */
  step?: number
  /** 是否显示标记 */
  marks?: boolean
  /** 自定义标记 */
  customMarks?: { value: number, label: string }[]
  /** 是否范围滑块 */
  range?: boolean
}

// 富文本编辑器配置
export interface RichTextConfig {
  /** 工具栏配置 */
  toolbar?: string[]
  /** 高度 */
  height?: number
  /** 是否启用文件上传 */
  uploadEnabled?: boolean
  /** 文件上传配置 */
  uploadConfig?: FileUploadConfig
}

// 代码编辑器配置
export interface CodeEditorConfig {
  /** 编程语言 */
  language?: string
  /** 主题 */
  theme?: 'light' | 'dark'
  /** 是否显示行号 */
  lineNumbers?: boolean
  /** 是否启用代码折叠 */
  folding?: boolean
  /** 高度 */
  height?: number
}

export type FormFieldOptions = ReadonlyArray<{
  label: string
  value: any
  disabled?: boolean
}>

export interface FormFieldConfig<T = any> {
  key: keyof T
  label: string
  type: FormFieldType
  placeholder?: string
  required?: boolean
  disabled?: boolean | ((formData: Record<string, any>) => boolean)
  show?: boolean | ((formData: Record<string, any>) => boolean)
  defaultValue?: any

  // 基础选项配置（select, radio-group, checkbox-group 等）
  options?: FormFieldOptions

  // 验证规则
  rules?: ValidationRule[]

  // 列宽配置（1-12，对应 grid-cols）
  col?: number

  // 组件特定属性
  componentProps?: Record<string, any>

  // 特殊字段类型配置
  /** combobox 远程搜索配置 */
  remoteSearch?: RemoteSearchConfig

  /** 文件上传配置 */
  fileConfig?: FileUploadConfig

  /** 日期范围配置 */
  dateRangeConfig?: DateRangeConfig

  /** 滑块配置 */
  sliderConfig?: SliderConfig

  /** 富文本编辑器配置 */
  richTextConfig?: RichTextConfig

  /** 代码编辑器配置 */
  codeConfig?: CodeEditorConfig

  /** 数字字段配置 */
  numberConfig?: {
    min?: number
    max?: number
    step?: number
    precision?: number
    formatOptions?: Intl.NumberFormatOptions
  }

  /** 标签输入配置 */
  tagsConfig?: {
    /** 最大标签数 */
    maxTags?: number
    /** 允许重复标签 */
    allowDuplicates?: boolean
    /** 标签验证函数 */
    validateTag?: (tag: string) => boolean
    /** 自动完成选项 */
    suggestions?: string[]
  }

  /** PIN 码输入配置 */
  pinConfig?: {
    /** PIN 码长度 */
    length?: number
    /** 是否遮蔽输入 */
    mask?: boolean
    /** 分隔符 */
    separator?: string
  }

  /** 评分组件配置 */
  ratingConfig?: {
    /** 最大评分 */
    max?: number
    /** 是否允许半星 */
    allowHalf?: boolean
    /** 自定义图标 */
    icon?: string
    /** 是否只读 */
    readonly?: boolean
  }

  /** 颜色选择器配置 */
  colorConfig?: {
    /** 颜色格式 */
    format?: 'hex' | 'rgb' | 'hsl'
    /** 预设颜色 */
    presetColors?: string[]
    /** 是否显示透明度 */
    showAlpha?: boolean
  }

  /** 自定义组件配置 */
  customComponent?: {
    /** 组件名称或组件实例 */
    component: any
    /** 传递给自定义组件的 props */
    props?: Record<string, any>
    /** 自定义事件映射 */
    events?: Record<string, string>
  }

  // === 字段联动相关配置 ===

  /** 字段联动配置 - 根据其他字段值动态更新选项 */
  linkage?: FieldLinkageConfig

  /** 字段值变化监听器 - 监听其他字段变化并执行自定义逻辑 */
  watcher?: FieldWatchConfig

  /** 字段级联配置 - 值变化时自动设置其他字段的值 */
  cascade?: FieldCascadeConfig

  /** 动态选项函数 - 基于表单数据动态生成选项 */
  dynamicOptions?: (formData: Record<string, any>) =>
    Promise<{ label: string, value: any, [key: string]: any }[]> | { label: string, value: any, [key: string]: any }[]

  /** 动态属性函数 - 基于表单数据动态设置字段属性 */
  dynamicProps?: (formData: Record<string, any>) => Record<string, any>
}

// 扩展的列配置，支持排序
export type ExtendedColumn<T = any> = Column<T> & {
  order?: number // 列的排序权重，数字越小越靠前
}

// 简化的 EasyCurd 配置
export interface EasyCurdConfig<T = any> {
  api: CurdApi<T>
  title?: string

  // 字段配置：只需配置一种，另一种自动生成
  columns?: Column<T>[]
  formFields?: FormFieldConfig<T>[]

  // 额外的表格列（不参与表单，只在表格中显示）
  extraColumns?: ExtendedColumn<T>[]

  // 全局列顺序配置（列的 key 或 id 数组）
  columnOrder?: string[]

  // 基础配置
  primaryKey?: string
  displayField?: string
  searchFields?: string[]
  sortFields?: string[]
  pageSize?: number
  pageSizeOptions?: number[]

  // 按钮文本配置
  btnText?: ButtonText

  // 功能开关
  features?: {
    create?: boolean
    edit?: boolean
    delete?: boolean
    batchDelete?: boolean
    search?: boolean
    export?: boolean
    import?: boolean
  }

  // 消息配置
  confirmMessages?: {
    delete?: string
    batchDelete?: string
  }

  successMessages?: {
    create?: string
    update?: string
    delete?: string
    batchDelete?: string
  }

  // hooks 钩子函数
  hooks?: CurdHooks<T>
}

/**
 * 自定义渲染插槽说明
 *
 * CurdTable 和 EasyCurd 组件支持以下自定义插槽：
 *
 * 1. 表头自定义渲染：
 *    - 插槽名格式：`header-{columnId}`
 *    - 插槽参数：{ header, column, context }
 *    - 示例：`<template #header-name="{ header, column }">自定义表头</template>`
 *
 * 2. 单元格自定义渲染：
 *    - 插槽名格式：`cell-{columnId}`
 *    - 插槽参数：{ cell, row, value, index, column, context }
 *    - 示例：`<template #cell-status="{ value, row }">{{ value ? '启用' : '禁用' }}</template>`
 *
 * 3. 操作列自定义渲染：
 *    - 插槽名：`actions`
 *    - 插槽参数：{ row, index }
 *    - 示例：`<template #actions="{ row }">自定义操作按钮</template>`
 *
 * 4. 工具栏自定义渲染：
 *    - 插槽名：`toolbar-left` 或 `toolbar-right`
 *    - 示例：`<template #toolbar-left>自定义工具栏</template>`
 */

// 自定义渲染插槽类型定义
export interface CustomRenderSlots<T = any> {
  // 表头插槽参数
  [key: `header-${string}`]: {
    header: any
    column: any
    context: any
  }

  // 单元格插槽参数
  [key: `cell-${string}`]: {
    cell: any
    row: T
    value: any
    index: number
    column: any
    context: any
  }

  'before-actions'?: {
    row: T
    index: number
  }

  'after-actions'?: {
    row: T
    index: number
  }

  // 操作列插槽参数
  'actions'?: {
    row: T
    index: number
  }

  // 工具栏插槽
  'toolbar-left'?: Record<string, never>
  'toolbar-right'?: Record<string, never>
}
