/**
 * UI 相关常量定义
 */

// 表格配置
export const TABLE_CONFIG = {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  MAX_ROWS_PER_PAGE: 100,
} as const

// 表单验证
export const FORM_VALIDATION = {
  MIN_PASSWORD_LENGTH: 6,
  MAX_PASSWORD_LENGTH: 50,
  MIN_NAME_LENGTH: 2,
  MAX_NAME_LENGTH: 50,
  EMAIL_PATTERN: /^[^\s@]+@[^\s@][^\s.@]*\.[^\s@]+$/,
  PHONE_PATTERN: /^1[3-9]\d{9}$/,
} as const

// 文件上传限制
export const FILE_UPLOAD = {
  MAX_SIZE_MB: 10,
  ALLOWED_IMAGE_TYPES: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  ALLOWED_DOCUMENT_TYPES: ['pdf', 'doc', 'docx', 'txt'],
  ALLOWED_EXTENSIONS: 'jpg,jpeg,png,gif,webp,pdf,doc,docx,txt',
} as const

// 动画和过渡时间
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  TOAST_DURATION: 5000,
  TOOLTIP_DELAY: 500,
} as const

// 布局断点
export const BREAKPOINTS = {
  'SM': 640,
  'MD': 768,
  'LG': 1024,
  'XL': 1280,
  '2XL': 1536,
} as const

// 图标大小
export const ICON_SIZES = {
  XS: 'w-3 h-3',
  SM: 'w-4 h-4',
  MD: 'w-5 h-5',
  LG: 'w-6 h-6',
  XL: 'w-8 h-8',
} as const

// 间距
export const SPACING = {
  XS: 'gap-1',
  SM: 'gap-2',
  MD: 'gap-4',
  LG: 'gap-6',
  XL: 'gap-8',
} as const
