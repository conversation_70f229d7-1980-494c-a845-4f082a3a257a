/**
 * 格式化函数单元测试
 */

import { describe, it, expect } from 'vitest'
import {
  formatCurrency,
  formatDate,
  formatNumber,
  formatFileSize,
  formatTimeAgo,
  formatUsageWithUnit,
} from '../format'

describe('格式化函数测试', () => {
  describe('formatCurrency', () => {
    it('应该正确格式化人民币', () => {
      expect(formatCurrency(1234.56)).toBe('¥1,234.56')
      expect(formatCurrency(0)).toBe('¥0.00')
      expect(formatCurrency(1000000)).toBe('¥1,000,000.00')
    })

    it('应该支持不同货币', () => {
      expect(formatCurrency(1234.56, 'USD')).toBe('US$1,234.56')
    })
  })

  describe('formatDate', () => {
    it('应该正确格式化日期', () => {
      const date = new Date('2024-01-15T10:30:00')
      const result = formatDate(date)
      expect(result).toMatch(/2024\/01\/15/)
    })

    it('应该处理空值', () => {
      expect(formatDate('')).toBe('-')
      expect(formatDate(null as any)).toBe('-')
    })

    it('应该处理时间戳', () => {
      const timestamp = 1705312200000 // 2024-01-15T10:30:00
      const result = formatDate(timestamp)
      expect(result).toMatch(/2024\/01\/15/)
    })
  })

  describe('formatNumber', () => {
    it('应该正确格式化大数字', () => {
      expect(formatNumber(1234)).toBe('1.2K')
      expect(formatNumber(1234567)).toBe('1.2M')
      expect(formatNumber(999)).toBe('999')
    })
  })

  describe('formatFileSize', () => {
    it('应该正确格式化文件大小', () => {
      expect(formatFileSize(0)).toBe('0 B')
      expect(formatFileSize(1024)).toBe('1 KB')
      expect(formatFileSize(1048576)).toBe('1 MB')
      expect(formatFileSize(1073741824)).toBe('1 GB')
    })
  })

  describe('formatTimeAgo', () => {
    it('应该正确格式化相对时间', () => {
      const now = new Date()
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

      expect(formatTimeAgo(now)).toBe('今天')
      expect(formatTimeAgo(yesterday)).toBe('昨天')
      expect(formatTimeAgo(weekAgo)).toBe('1周前')
    })

    it('应该处理空值', () => {
      expect(formatTimeAgo('')).toBe('从未')
      expect(formatTimeAgo(null as any)).toBe('从未')
    })
  })

  describe('formatUsageWithUnit', () => {
    it('应该正确格式化token使用量', () => {
      expect(formatUsageWithUnit(1000, 'token')).toBe('1.0K tokens')
      expect(formatUsageWithUnit(1000000, 'token')).toBe('1.0M tokens')
      expect(formatUsageWithUnit(500, 'token')).toBe('500 tokens')
    })

    it('应该正确格式化字符使用量', () => {
      expect(formatUsageWithUnit(1000, 'character')).toBe('1.0K 字符')
      expect(formatUsageWithUnit(500, 'character')).toBe('500 字符')
    })

    it('应该正确格式化秒数', () => {
      expect(formatUsageWithUnit(3661, 'second')).toBe('1小时1分1秒')
      expect(formatUsageWithUnit(61, 'second')).toBe('1分1秒')
      expect(formatUsageWithUnit(30, 'second')).toBe('30s')
    })
  })
})
