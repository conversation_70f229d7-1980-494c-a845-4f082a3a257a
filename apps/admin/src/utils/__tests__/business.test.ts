/**
 * 业务函数单元测试
 */

import { describe, it, expect } from 'vitest'
import {
  getModuleName,
  getStatusColor,
  getQuotaStatusVariant,
  maskApiKey,
  getUsageRate,
  isQuotaExpired,
} from '../business'

describe('业务函数测试', () => {
  describe('getModuleName', () => {
    it('应该返回正确的模块名称', () => {
      expect(getModuleName('llm')).toBe('LLM')
      expect(getModuleName('tts')).toBe('TTS语音合成')
      expect(getModuleName('asr')).toBe('ASR语音识别')
      expect(getModuleName('unknown')).toBe('unknown')
    })
  })

  describe('getStatusColor', () => {
    it('应该返回正确的状态颜色', () => {
      expect(getStatusColor('ok')).toBe('default')
      expect(getStatusColor('blocked')).toBe('destructive')
      expect(getStatusColor('other')).toBe('destructive')
    })
  })

  describe('getQuotaStatusVariant', () => {
    it('应该返回正确的配额状态变体', () => {
      expect(getQuotaStatusVariant('active')).toBe('default')
      expect(getQuotaStatusVariant('expired')).toBe('secondary')
      expect(getQuotaStatusVariant('exhausted')).toBe('outline')
      expect(getQuotaStatusVariant('disabled')).toBe('destructive')
      expect(getQuotaStatusVariant('unknown')).toBe('secondary')
    })
  })

  describe('maskApiKey', () => {
    it('应该正确遮蔽API Key', () => {
      const apiKey = 'sk-1234567890abcdef1234567890abcdef'
      const masked = maskApiKey(apiKey)
      expect(masked).toBe('sk-1****cdef')
      expect(masked.length).toBe(10)
    })

    it('应该处理空值', () => {
      expect(maskApiKey('')).toBe('')
      expect(maskApiKey(null as any)).toBe('')
    })

    it('应该处理短字符串', () => {
      expect(maskApiKey('abc')).toBe('abc')
      expect(maskApiKey('abcdefgh')).toBe('abcd****efgh')
    })
  })

  describe('getUsageRate', () => {
    it('应该正确计算使用率', () => {
      expect(getUsageRate(50, 100)).toBe('50.00')
      expect(getUsageRate(33, 100)).toBe('33.00')
      expect(getUsageRate(0, 100)).toBe('0.00')
    })

    it('应该处理除零情况', () => {
      expect(getUsageRate(50, 0)).toBe('0')
      expect(getUsageRate(0, 0)).toBe('0')
    })
  })

  describe('isQuotaExpired', () => {
    it('应该正确判断配额是否过期', () => {
      const now = Date.now()
      const pastTime = now - 1000 // 1秒前
      const futureTime = now + 1000 // 1秒后

      expect(isQuotaExpired(pastTime)).toBe(true)
      expect(isQuotaExpired(futureTime)).toBe(false)
    })

    it('应该处理空值', () => {
      expect(isQuotaExpired()).toBe(false)
      expect(isQuotaExpired(undefined)).toBe(false)
      expect(isQuotaExpired(null as any)).toBe(false)
    })
  })
})
