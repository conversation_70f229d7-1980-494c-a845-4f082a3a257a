<script setup lang="ts">
import type { UsageLog } from '@/api/key'
import {
  Badge,
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@billing/ui'
import { History } from 'lucide-vue-next'
import { BILLING_TYPE } from '@/constants/common'
import {
  formatCurrency,
  formatTime,
  formatUsageWithUnit,
  getBillingTypeColor,
  getModuleName,
} from '@/utils'

interface Props {
  usageHistory: UsageLog[]
  currentPage: number
  totalPages: number
}

interface Emits {
  (e: 'update:currentPage', value: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

function handlePrevPage() {
  if (props.currentPage > 1) {
    emit('update:currentPage', props.currentPage - 1)
  }
}

function handleNextPage() {
  if (props.currentPage < props.totalPages) {
    emit('update:currentPage', props.currentPage + 1)
  }
}
</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle class="flex items-center gap-2">
        <History class="w-5 h-5" />
        使用历史
      </CardTitle>
    </CardHeader>
    <CardContent>
      <div
        v-if="usageHistory.length === 0"
        class="text-center py-8 text-gray-500"
      >
        暂无使用记录
      </div>
      <div
        v-else
        class="space-y-3"
      >
        <div
          v-for="log in usageHistory"
          :key="log.id"
          class="flex items-center justify-between p-3 border rounded-lg"
        >
          <div class="flex items-center gap-3">
            <Badge variant="outline">
              {{ getModuleName(log.module) }}
            </Badge>
            <Badge
              :variant="getBillingTypeColor(log.billing_type)"
              class="text-xs"
            >
              {{ log.billing_type === BILLING_TYPE.QUOTA ? '资源包' : '余额' }}
            </Badge>
            <div>
              <div class="font-medium text-sm">
                {{ log.model_name || '默认模型' }}
              </div>
              <div class="text-sm text-gray-500">
                {{ formatTime(log.created_at) }}
              </div>
            </div>
          </div>
          <div class="text-right">
            <div class="font-medium">
              {{ formatUsageWithUnit(log.usage, log.unit) }}
            </div>
            <div class="text-sm text-gray-500">
              {{ formatCurrency(log.cost) }}
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div
          v-if="totalPages > 1"
          class="flex justify-center pt-4"
        >
          <div class="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              :disabled="currentPage <= 1"
              @click="handlePrevPage"
            >
              上一页
            </Button>
            <span class="text-sm text-gray-500">
              第 {{ currentPage }} 页，共 {{ totalPages }} 页
            </span>
            <Button
              variant="outline"
              size="sm"
              :disabled="currentPage >= totalPages"
              @click="handleNextPage"
            >
              下一页
            </Button>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>
