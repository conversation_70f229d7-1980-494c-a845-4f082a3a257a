<script setup lang="ts">
import type { KeyStatusResponse, UsageLog, UsageStats } from '@/api/key'
import type { CreateQuotaPackageRequest, QuotaPackage } from '@/api/quota'
import { Alert, AlertDescription } from '@billing/ui'
import { Info } from 'lucide-vue-next'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { keyApi } from '@/api/key'
import { quotaApi } from '@/api/quota'
import { QUOTA_STATUS } from '@/constants/common'
import { getModuleName } from '@/utils'
import {
  KeyBasicInfo,
  QuotaEditDialog,
  QuotaManagement,
  UsageHistory,
  UsageStats as UsageStatsComponent,
} from './components'

const route = useRoute()
const keyValue = ref(route.params.key as string)

// 数据状态
const loading = ref(false)
const keyInfo = ref<KeyStatusResponse | null>(null)
const usageStats = ref<UsageStats | null>(null)
const usageHistory = ref<UsageLog[]>([])
const selectedPeriod = ref<'day' | 'week' | 'month'>('month')

// 配额编辑弹窗
const showQuotaDialog = ref(false)
const editingQuota = ref<QuotaPackage | null>(null)

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const totalPages = ref(0)

// 计算属性：计费方式描述
const billingModeDescription = computed(() => {
  if (!keyInfo.value)
    return ''

  const hasQuotas = keyInfo.value.quotas && keyInfo.value.quotas.length > 0
  const hasBalance = keyInfo.value.user && keyInfo.value.user.balance > 0

  if (hasQuotas && hasBalance) {
    return '混合计费：优先使用资源包，资源包用完后扣除用户余额'
  }
  else if (hasQuotas) {
    return '资源包计费：仅使用配置的资源包'
  }
  else if (hasBalance) {
    return '余额计费：直接扣除用户余额'
  }
  else {
    return '无可用计费方式：请添加资源包或为用户充值'
  }
})

// 计算属性：详细配额统计，区分具体模型
const quotaStats = computed(() => {
  if (!keyInfo.value?.quotas)
    return null

  const detailedStats: Array<{
    key: string
    module: string
    modelName: string
    displayName: string
    total: number
    used: number
    available: number
    quotas: any[]
  }> = []

  // 按模块和模型分组
  const groupedQuotas = new Map<string, any[]>()

  keyInfo.value.quotas.forEach((quota) => {
    // 创建唯一key：模块+模型名（如果有的话）
    const modelKey = quota.model_name || 'general'
    const groupKey = `${quota.module}-${modelKey}`

    if (!groupedQuotas.has(groupKey)) {
      groupedQuotas.set(groupKey, [])
    }
    groupedQuotas.get(groupKey)!.push(quota)
  })

  // 生成统计数据
  groupedQuotas.forEach((quotas, groupKey) => {
    const [module, modelKey] = groupKey.split('-')
    const modelName = modelKey === 'general' ? '' : modelKey

    const total = quotas.reduce((sum, q) => sum + q.quota, 0)
    const used = quotas.reduce((sum, q) => sum + q.used, 0)
    const available = quotas.reduce((sum, q) => sum + q.available, 0)

    // 生成显示名称
    let displayName = getModuleName(module)
    if (modelName) {
      displayName += ` (${modelName})`
    }
    else {
      displayName += ' (通用)'
    }

    detailedStats.push({
      key: groupKey,
      module,
      modelName,
      displayName,
      total,
      used,
      available,
      quotas,
    })
  })

  // 按模块类型和模型名称排序
  detailedStats.sort((a, b) => {
    if (a.module !== b.module) {
      return a.module.localeCompare(b.module)
    }
    // 通用模型排在前面
    if (!a.modelName && b.modelName)
      return -1
    if (a.modelName && !b.modelName)
      return 1
    return a.modelName.localeCompare(b.modelName)
  })

  return detailedStats
})

// 获取Key状态信息
async function fetchKeyInfo() {
  if (!keyValue.value)
    return

  loading.value = true
  try {
    keyInfo.value = await keyApi.getKeyStatus(keyValue.value)
  }
  catch (error) {
    console.error('获取Key信息失败:', error)
  }
  finally {
    loading.value = false
  }
}

// 获取用量统计
async function fetchUsageStats() {
  if (!keyValue.value)
    return

  try {
    usageStats.value = await keyApi.getUsageStats(keyValue.value, selectedPeriod.value)
  }
  catch (error) {
    console.error('获取用量统计失败:', error)
  }
}

// 获取用量历史
async function fetchUsageHistory() {
  if (!keyValue.value)
    return

  try {
    const result = await keyApi.getUsageHistory(keyValue.value, {
      page: currentPage.value,
      page_size: pageSize.value,
    })
    usageHistory.value = result.data
    totalPages.value = result.pagination.total_pages
  }
  catch (error) {
    console.error('获取用量历史失败:', error)
  }
}

// 新增配额
function addNewQuota() {
  editingQuota.value = null
  showQuotaDialog.value = true
}

// 处理配额表单提交
async function handleQuotaFormSubmit(formData: any) {
  if (!keyInfo.value?.user)
    return

  try {
    const createData: CreateQuotaPackageRequest = {
      user_id: keyInfo.value.user.id,
      api_key: keyValue.value,
      module: formData.module,
      quota: formData.quota,
      expires_at: formData.expires_at || undefined,
      model_name: formData.model_name || undefined,
      description: formData.description,
      type: 'admin', // 管理员创建的配额
    }

    if (editingQuota.value) {
      // 编辑模式 - 这里需要调用更新API
      console.log('编辑配额:', editingQuota.value.id, createData)
    }
    else {
      // 新增模式
      await quotaApi.createItem(createData)
    }

    showQuotaDialog.value = false
    await fetchKeyInfo()
  }
  catch (error) {
    console.error('更新配额失败:', error)
  }
}

// 切换配额状态
async function toggleQuotaStatus(quota: QuotaPackage) {
  try {
    if (quota.status === QUOTA_STATUS.ACTIVE) {
      await quotaApi.disableItem(quota.id)
    }
    else {
      await quotaApi.enableItem(quota.id)
    }
    await fetchKeyInfo()
  }
  catch (error) {
    console.error('切换配额状态失败:', error)
  }
}

// 监听周期变化
watch(selectedPeriod, fetchUsageStats)

// 监听分页变化
watch([currentPage], fetchUsageHistory)

// 初始化数据
onMounted(async () => {
  await Promise.all([
    fetchKeyInfo(),
    fetchUsageStats(),
    fetchUsageHistory(),
  ])
})
</script>

<template>
  <div class="space-y-6">
    <!-- 顶部导航 -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-4">
        <!-- <Button
          variant="ghost"
          @click="goBack"
        >
          <ArrowLeft class="w-4 h-4" />
          返回
        </Button> -->
        <div>
          <h1 class="text-2xl font-semibold text-gray-900">
            API Key 详情
          </h1>
          <p class="mt-1 text-sm text-gray-500">
            {{ keyValue }}
          </p>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div
      v-if="loading"
      class="flex justify-center py-8"
    >
      <div class="text-gray-500">
        加载中...
      </div>
    </div>

    <template v-else-if="keyInfo">
      <!-- 计费方式说明 -->
      <Alert>
        <Info class="w-4 h-4" />
        <AlertDescription>
          <strong>计费说明：</strong>{{ billingModeDescription }}
        </AlertDescription>
      </Alert>

      <!-- Key基本信息组件 -->
      <KeyBasicInfo
        :key-info="keyInfo"
        :quota-stats="quotaStats"
      />

      <!-- 资源包管理组件 -->
      <QuotaManagement
        :key-info="keyInfo"
        @add-quota="addNewQuota"
        @toggle-quota-status="toggleQuotaStatus"
      />

      <!-- 用量统计组件 -->
      <UsageStatsComponent
        :usage-stats="usageStats"
        :selected-period="selectedPeriod"
        @update:selected-period="selectedPeriod = $event"
      />

      <!-- 使用历史组件 -->
      <UsageHistory
        :usage-history="usageHistory"
        :current-page="currentPage"
        :total-pages="totalPages"
        @update:current-page="currentPage = $event"
      />

      <!-- 配额编辑弹窗 -->
      <QuotaEditDialog
        :open="showQuotaDialog"
        :editing-quota="editingQuota"
        @update:open="showQuotaDialog = $event"
        @confirm="handleQuotaFormSubmit"
      />
    </template>
  </div>
</template>
